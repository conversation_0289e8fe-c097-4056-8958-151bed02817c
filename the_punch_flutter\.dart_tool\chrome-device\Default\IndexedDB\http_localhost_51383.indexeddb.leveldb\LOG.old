2025/06/25-13:50:14.926 4b10 Reusing MANIFEST C:\Users\<USER>\AppData\Local\Temp\flutter_tools.d8a71cf3\flutter_tools_chrome_device.678d66a8\Default\IndexedDB\http_localhost_51383.indexeddb.leveldb/MANIFEST-000001
2025/06/25-13:50:14.928 4b10 Recovering log #100
2025/06/25-13:50:14.929 4b10 Reusing old log C:\Users\<USER>\AppData\Local\Temp\flutter_tools.d8a71cf3\flutter_tools_chrome_device.678d66a8\Default\IndexedDB\http_localhost_51383.indexeddb.leveldb/000100.log 
2025/06/25-13:50:14.980 10b0 Level-0 table #107: started
2025/06/25-13:50:14.985 10b0 Level-0 table #107: 2047 bytes OK
2025/06/25-13:50:14.988 10b0 Delete type=0 #100
2025/06/25-13:50:14.989 10b0 Manual compaction at level-0 from '\x00&\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00'\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/25-13:50:14.996 4b10 Compacting 1@1 + 3@2 files
2025/06/25-13:50:15.138 4b10 Generated table #108@1: 11257 keys, 2165369 bytes
2025/06/25-13:50:15.264 4b10 Generated table #109@1: 15543 keys, 2169145 bytes
2025/06/25-13:50:15.282 4b10 Generated table #110@1: 3401 keys, 191826 bytes
2025/06/25-13:50:15.282 4b10 Compacted 1@1 + 3@2 files => 4526340 bytes
2025/06/25-13:50:15.284 4b10 compacted to: files[ 0 0 3 0 0 0 0 ]
2025/06/25-13:50:15.285 4b10 Delete type=2 #102
2025/06/25-13:50:15.285 4b10 Delete type=2 #103
2025/06/25-13:50:15.285 4b10 Delete type=2 #104
2025/06/25-13:50:15.286 4b10 Delete type=2 #107

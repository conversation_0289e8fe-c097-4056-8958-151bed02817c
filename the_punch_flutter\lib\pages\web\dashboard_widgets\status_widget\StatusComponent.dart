// ignore_for_file: prefer_const_literals_to_create_immutables

import 'package:flutter/material.dart';

class StatusComponent extends StatelessWidget {
  final int liveCount;
  final int scheduledCount;
  final int completedCount;
  final Map<String, int> alerts;
  final dynamic viewModel;
 
  StatusComponent({super.key, 
    this.liveCount = 0,
    this.scheduledCount = 0,
    this.completedCount = 0,
    this.alerts = const {
      'Early/Late In': 16,
      'Early/Late Out': 18,
      'Geofence Breach': 23,
      'No Show': 207,
    },
    required this.viewModel,
  });
 
  @override
  Widget build(BuildContext context) {
     final isMobile = MediaQuery.of(context).size.width < 600;
    return  Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.centerRight,
          end: Alignment.centerLeft,
          colors: [
            Color.fromRGBO(198, 222, 246, 1),
            Color.fromRGBO(235, 246, 255, 1),
          ],
        ),
        borderRadius: isMobile ? BorderRadius.circular(0) : BorderRadius.circular(32),
      ),
      padding: isMobile ? const EdgeInsets.all(10) : const EdgeInsets.all(32),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
         
          _buildFIlters(viewModel, 20, 1),
         if(!isMobile)
          const Divider(
            color: Color.fromRGBO(9, 31, 48, 0.12),
            height: 32,
          ),
          if(!isMobile)
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              StatusCounts(
                liveCount: viewModel.liveCount,
                scheduledCount: viewModel.scheduledCount,
                completedCount: viewModel.completedCount,
              ),
              const SizedBox(width: 70),
              AlertsWidget(alerts: {
                  'Early/Late In': viewModel.earlyLatePunchInCount,
                  'Early/Late Out': viewModel.earlyLatePunchOutCount,
                  'Geofence Breach': viewModel.geofenceBreachCount,
                  'No Show': viewModel.noShowCount,
                  
                },
                viewModel:viewModel
                ),
            ],
          ),
        ],
      ),
    );
  }
Row _buildFIlters(viewModel, double fontSize, double scaleFactor) => Row(
  mainAxisAlignment: MainAxisAlignment.spaceBetween,
  children: [
    _FilterButton(
      label: 'Today',
      filterKey: 'today',
      textColor: viewModel.selectedFilter == 'today' ? Colors.white : Colors.black,
      scaleFactor: scaleFactor,
      onSelected: viewModel.applyFilter,
      isSelected: viewModel.selectedFilter == 'today',
    ),
    _FilterButton(
      label: 'Week',
      filterKey: 'week',
      textColor: viewModel.selectedFilter == 'week' ? Colors.white : Colors.black,
      scaleFactor: scaleFactor,
      onSelected: viewModel.applyFilter,
      isSelected: viewModel.selectedFilter == 'week',
    ),
    _FilterButton(
      label: 'Month',
      filterKey: 'month',
      textColor: viewModel.selectedFilter == 'month' ? Colors.white : Colors.black,
      scaleFactor: scaleFactor,
      onSelected: viewModel.applyFilter,
      isSelected: viewModel.selectedFilter == 'month',
    ),
    _FilterButton(
      label: 'Live',
      filterKey: 'live',
      textColor: viewModel.selectedFilter == 'live' ? Colors.white : Colors.black,
      scaleFactor: scaleFactor,
      onSelected: viewModel.applyFilter,
      isSelected: viewModel.selectedFilter == 'live',
    ),
    _FilterButton(
      label: 'Scheduled',
      filterKey: 'scheduled',
      textColor: viewModel.selectedFilter == 'scheduled' ? Colors.white : Colors.black,
      scaleFactor: scaleFactor,
      onSelected: viewModel.applyFilter,
      isSelected: viewModel.selectedFilter == 'scheduled',
    ),
  ],
    );
}
class _FilterButton extends StatefulWidget {
  final String label;
  final String filterKey;
  final Color textColor;
  final bool isSelected;
  final Function(String) onSelected;
  final double scaleFactor;

  const _FilterButton({
    Key? key,
    required this.label,
    required this.filterKey,
    required this.textColor,
    required this.isSelected,
    required this.onSelected,
    required this.scaleFactor,
  }) : super(key: key);

  @override
  State<_FilterButton> createState() => _FilterButtonState();
}
class _FilterButtonState extends State<_FilterButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => MouseRegion(
        onEnter: (_) => _controller.forward(),
        onExit: (_) => _controller.reverse(),
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: GestureDetector(
            onTap: () => widget.onSelected(widget.filterKey),
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: 16 * widget.scaleFactor,
                vertical: 8 * widget.scaleFactor,
              ),
              decoration: BoxDecoration(
                color: widget.isSelected
                    ? const Color(0xFF4BA2E7) // Blue background for selected
                    : Colors.white, // Black background for unselected
                borderRadius: BorderRadius.circular(20 * widget.scaleFactor),
              ),
              child: Text(
                widget.label,
                style: TextStyle(
                  color: widget.isSelected ? Colors.white : Colors.black, // White text color for better contrast
                  fontSize: 12 * widget.scaleFactor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      );
    }
class FilterTabs extends StatelessWidget {
  const FilterTabs({super.key});

  @override
  Widget build(BuildContext context) => DefaultTabController(
      length: 5,
      child: TabBar(
        isScrollable: true,
        indicator: BoxDecoration(
          color: const Color(0xFF4BA2E7),
          borderRadius: BorderRadius.circular(100),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: const Color(0xFF192229),
        tabs: [
          const Tab(text: 'Today'),
          const Tab(text: 'Week'),
          const Tab(text: 'Month'),
          const Tab(text: 'Live'),
          const Tab(text: 'Scheduled'),
        ],
      ),
    );
}

class StatusCounts extends StatelessWidget {
  final int liveCount;
  final int scheduledCount;
  final int completedCount;

  StatusCounts({super.key, 
    required this.liveCount,
    required this.scheduledCount,
    required this.completedCount,
  });

  @override
  Widget build(BuildContext context) => Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Status Counts',
          style: TextStyle(
            fontFamily: 'Poppins',
            fontSize: 14,
            fontWeight: FontWeight.w700,
            letterSpacing: -0.14,
            color: Color(0xFF091F30),
          ),
        ),
        const SizedBox(height: 14),
        Row(
          children: [
            CountItem(label: 'Live', count: liveCount.toString()),
            const SizedBox(width: 16),
            CountItem(label: 'Scheduled', count: scheduledCount.toString()),
            
          ],
        ),
        const SizedBox(height: 10),
            CountItem(label: 'Completed', count: completedCount.toString()),
      ],
    );
}

class CountItem extends StatelessWidget {
  final String label;
  final String count;

  CountItem({super.key, required this.label, required this.count});

  @override
  Widget build(BuildContext context) => Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontFamily: 'Poppins',
            fontSize: 12,
            fontWeight: FontWeight.bold,
            letterSpacing: -0.12,
            color: Color(0xFF091F30),
          ),
        ),
        Text(
          count,
          style: const TextStyle(
            fontFamily: 'Poppins',
            fontSize: 20,
            fontWeight: FontWeight.w700,
            letterSpacing: -0.2,
            color: Color(0xFF091F30),
          ),
        ),
      ],
    );
}

class AlertsWidget extends StatelessWidget {
  final Map<String, int> alerts;
  final  viewModel;
  AlertsWidget({super.key, required this.alerts,required this.viewModel});

  @override
  Widget build(BuildContext context) => Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Alerts',
          style: TextStyle(
            fontFamily: 'Poppins',
            fontSize: 14,
            fontWeight: FontWeight.w700,
            letterSpacing: -0.14,
            color: Color(0xFF091F30),
          ),
        ),
        const SizedBox(height: 4),
        ...alerts.entries.map((entry) {
           var key = 'earlyLatePunchIn';
           if(entry.key=='Early/Late Punch-Out:'){
            key = 'earlyLatePunchOut';
           }
           if(entry.key=='Geofence Breached:'){
            key = 'geofenceBreach';
           }
           if(entry.key=='No Show:'){
            key = 'noShow';
           }
             return MouseRegion(
             onEnter: (_) => print('Mouse entered ${entry.key}'),
             onExit: (_) => print('Mouse exited ${entry.key}'),
             child: Padding(
               padding: const EdgeInsets.only(top: 8.0, right: 8.0, bottom: 8.0),
               child: GestureDetector(
               onTap: () => viewModel.applyFilter(key),
               child: AlertItem(
                 color: _getAlertColor(entry.key),
                 label: entry.key,
                 count: entry.value.toString(),
                 filterKey: key,
                 onSelected: viewModel.applyFilter,
                 isSelected: viewModel.selectedFilter == key,
               ),
               ),
             ),
             
             
             );
        })
         
      ]
    );

  Color _getAlertColor(String label) {
    switch (label) {
      case 'Early/Late In':
        return const Color(0xFFFED818);
      case 'Early/Late Out':
        return const Color(0xFFFD7B17);
      case 'Geofence Breach':
        return const Color(0xFFFD1717);
      case 'No Show':
        return const Color(0xFFB34023);
      default:
        return Colors.grey;
    }
  }
}

class AlertItem extends StatelessWidget {
  final Color color;
  final String label;
  final String count;
 final String filterKey;
   final bool isSelected;
  final Function(String) onSelected;
  AlertItem({super.key, 
    required this.color,
    required this.label,
    required this.count,
     required this.filterKey, required this.isSelected, required this.onSelected,
  });

  @override
  Widget build(BuildContext context) => Row(
      children: [
        Container(
          width: 14,
          height: 14,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 2),
          ),
        ),
        const SizedBox(width: 6),
        Text(
          label,
          style: const TextStyle(
            fontFamily: 'Poppins',
            fontSize: 12,
            fontWeight: FontWeight.bold,
            letterSpacing: -0.12,
            color: Color(0xFF091F30),
          ),
        ),
        const SizedBox(width: 6),
        Container(
          width: 4,
          height: 4,
          decoration: const BoxDecoration(
            color: Color(0xFF091F30),
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 6),
        Text(
          count,
          style: const TextStyle(
            fontFamily: 'Poppins',
            fontSize: 12,
            fontWeight: FontWeight.w700,
            letterSpacing: -0.12,
            color: Color(0xFF091F30),
          ),
        ),
      ],
    );
}


class _AlertItem extends StatefulWidget {
  final String title;
  final String filterKey;
  final int count;
  final Color color;
  final double fontSize;
  final double scaleFactor;
  final bool isSelected;
  final Function(String) onSelected;

  const _AlertItem({
    Key? key,
    required this.title,
    required this.filterKey,
    required this.count,
    required this.color,
    required this.fontSize,
    required this.scaleFactor,
    required this.isSelected,
    required this.onSelected,
  }) : super(key: key);

  @override
  State<_AlertItem> createState() => _AlertItemState();
}

class _AlertItemState extends State<_AlertItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => MouseRegion(
        onEnter: (_) => _controller.forward(),
        onExit: (_) => _controller.reverse(),
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: GestureDetector(
            onTap: () => widget.onSelected(widget.filterKey),
            child: AlertItem(isSelected: widget.isSelected,onSelected: widget.onSelected, color: widget.color, count: widget.count.toString(), label: widget.title, filterKey: widget.filterKey,),
            //  Container(
            //   margin: EdgeInsets.symmetric(vertical: 4 * widget.scaleFactor),
            //   padding: EdgeInsets.symmetric(
            //       horizontal: 16 * widget.scaleFactor,
            //       vertical: 12 * widget.scaleFactor),
            //   decoration: BoxDecoration(
            //     color: widget.isSelected
            //         ? widget.color.withOpacity(0.7)
            //         : widget.color.withOpacity(0.9),
            //     borderRadius: BorderRadius.circular(12 * widget.scaleFactor),
            //     boxShadow: [
            //       BoxShadow(
            //         color: Colors.black.withOpacity(0.1),
            //         blurRadius: 8 * widget.scaleFactor,
            //         offset: Offset(0, 4 * widget.scaleFactor),
            //       ),
            //     ],
            //   ),
            //   child: Row(
            //     mainAxisSize: MainAxisSize.min,
            //     children: [
            //       Icon(
            //         Icons.warning_amber_rounded,
            //         color: Colors.white,
            //         size: 15 * widget.scaleFactor,
            //       ),
            //       SizedBox(width: 8 * widget.scaleFactor),
            //       Text(
            //         '${widget.title} ${widget.count}',
            //         style: TextStyle(
            //           fontSize: (widget.fontSize * widget.scaleFactor) - 2,
            //           color: Colors.white,
            //           fontWeight: FontWeight.w600,
            //         ),
            //       ),
            //     ],
            //   ),
            // ),
         
          ),
        ),
      );
}

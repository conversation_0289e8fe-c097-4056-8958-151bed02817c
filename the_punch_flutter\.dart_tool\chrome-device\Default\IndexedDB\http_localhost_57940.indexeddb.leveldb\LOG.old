2025/06/25-11:48:09.749 4300 Reusing MANIFEST C:\Users\<USER>\AppData\Local\Temp\flutter_tools.d59a12f1\flutter_tools_chrome_device.c786bdee\Default\IndexedDB\http_localhost_57940.indexeddb.leveldb/MANIFEST-000001
2025/06/25-11:48:09.751 4300 Recovering log #49
2025/06/25-11:48:09.752 4300 Reusing old log C:\Users\<USER>\AppData\Local\Temp\flutter_tools.d59a12f1\flutter_tools_chrome_device.c786bdee\Default\IndexedDB\http_localhost_57940.indexeddb.leveldb/000049.log 
2025/06/25-11:48:09.952 a0d8 Level-0 table #56: started
2025/06/25-11:48:09.959 a0d8 Level-0 table #56: 2053 bytes OK
2025/06/25-11:48:09.962 a0d8 Delete type=0 #49
2025/06/25-11:48:09.964 4300 Manual compaction at level-0 from '\x00"\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00#\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/25-11:48:10.115 70ec Compacting 1@1 + 3@2 files
2025/06/25-11:48:10.248 70ec Generated table #57@1: 11256 keys, 2165284 bytes
2025/06/25-11:48:10.408 70ec Generated table #58@1: 15543 keys, 2169947 bytes
2025/06/25-11:48:10.430 70ec Generated table #59@1: 3401 keys, 192189 bytes
2025/06/25-11:48:10.431 70ec Compacted 1@1 + 3@2 files => 4527420 bytes
2025/06/25-11:48:10.432 70ec compacted to: files[ 0 0 3 0 0 0 0 ]
2025/06/25-11:48:10.433 70ec Delete type=2 #51
2025/06/25-11:48:10.434 70ec Delete type=2 #52
2025/06/25-11:48:10.434 70ec Delete type=2 #53
2025/06/25-11:48:10.434 70ec Delete type=2 #56

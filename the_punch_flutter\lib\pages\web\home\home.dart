// ignore_for_file: prefer_expression_function_bodies, discarded_futures

import 'dart:async';
import 'dart:io' show File;
import 'dart:typed_data'; // For web in-memory bytes
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:collection/collection.dart';
import 'package:the_punch_flutter/state/server_time_state.dart';
import 'package:uuid/uuid.dart';

import '../../../api/api_model.dart';
import '../../../dataModel/data/inspection.dart';
import '../../../dataModel/data/location.dart';
import '../../../dataModel/data/punch_card.dart';
import '../../../dataModel/data/schedule.dart';
import '../../../dataModel/data/user.dart';
import '../../../dataModel/data_model.dart';
import '../../../helpers/color_helper.dart';
import '../../../misc/app_localization.dart';
import '../../../misc/extensions.dart';
import '../../../services/cached_image_service.dart';
import '../../../state/login_state.dart';
import '../../../state/permissions_state.dart';
import '../../../widgets/menu/my_tab_bar.dart';
import '../my_scaffold.dart';
import 'month_box.dart';
import '../../view_model_mixin.dart';

// -----------------------------------------------------------------------------
// 2) The rest of your home page code
// -----------------------------------------------------------------------------

// Scroll Behavior Customization
class CustomScrollBehavior extends ScrollBehavior {
  @override
  Widget buildViewportChrome(
    BuildContext context,
    Widget child,
    AxisDirection axisDirection,
  ) {
    return child; // Makes the scrollbar invisible
  }
}

// Home Page Widget
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    // Enable Crashlytics and log the result
    _enableCrashlytics();

    return ChangeNotifierProvider<_ViewModel>(
      create: (context) => _ViewModel()..initialize(context),
      child: Consumer<_ViewModel>(
        builder: (context, viewModel, child) {
          if (viewModel.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }
          return MyScaffold(
            title: AppLocalization.of(context).home,
            body: ScrollConfiguration(
              behavior: CustomScrollBehavior(),
              child: LayoutBuilder(
                builder: (context, constraints) =>  _buildDashboardResponsive(context, constraints, viewModel),
                
              ),
            ),
          );
        },
      ),
    );
  }

  // Firebase Crashlytics enabling method
  Future<void> _enableCrashlytics() async {
    try {
      await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
      print('Crashlytics enabled on HomePage');
    } catch (e) {
      print('Failed to enable Crashlytics on HomePage: $e');
    }
  }

  Widget _buildDashboardResponsive(
    BuildContext context,
    BoxConstraints constraints,
    _ViewModel viewModel,
  ) {
    double maxWidth = constraints.maxWidth;

    bool isMobileVP = maxWidth <= 675;
    bool isTabletVP = maxWidth > 675 && maxWidth <= 1024;
    bool isDesktopVP = maxWidth > 1024;

    double containerWidth = isDesktopVP ? 970 : (isTabletVP ? 800 : maxWidth);

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            const SizedBox(height: 20),

            // Example punch-card UI
          //  if (isMobileVP) _buildLargePunchCard(isMobileVP, viewModel),
            if (isTabletVP || isDesktopVP)
              _buildDesktopPunchCard(containerWidth, viewModel),
          //  const SizedBox(height: 20),
           // if (isMobileVP || isTabletVP) const SizedBox(height: 20),
           // if (isMobileVP) _buildPunchCardsContainer(containerWidth, false),
           // const SizedBox(height: 20),
            if (isDesktopVP) _buildDesktopViewOther(containerWidth),
            if (isTabletVP) _buildTabletViewOther(containerWidth),
            if (isMobileVP) _buildMobileViewOther(context, containerWidth),
          ],
        ),
      ),
    );
  }

  Widget _buildLargePunchCard(bool isMobileVP, _ViewModel viewModel) {
    return SizedBox(
      width: double.infinity,
      height: isMobileVP ? 200 : 250,
      child: PunchCardBigCountBoxRed(
        'All Punch Cards',
        viewModel.allCount?.toString() ?? '0',
      ),
    );
  }

  Widget _buildDesktopPunchCard(double containerWidth, _ViewModel viewModel) {
    return SizedBox(
      width: containerWidth,
      child: Row(
        children: [
          const Expanded(child: _PunchCards(isMobileVP: false)),
          const SizedBox(width: 20),
          SizedBox(
            width: 300,
            height: 400,
            child: PunchCardBigCountBoxRed(
              'All Punch Cards',
              viewModel.allCount?.toString() ?? '0',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPunchCardsContainer(double containerWidth, bool isMobileVP) {
    return SizedBox(
      width: containerWidth,
      child: _PunchCards(isMobileVP: isMobileVP),
    );
  }

  Widget _buildTabletViewOther(double containerWidth) {
    return SizedBox(
      width: containerWidth,
      child: Column(
        children: [
          Row(
            children: [
              Expanded(child: _Employees()),
              const SizedBox(width: 20),
              Expanded(child: _Locations()),
            ],
          ),
          const SizedBox(height: 50),
          _buildCalendarForTablet(),
        ],
      ),
    );
  }

  Widget _buildMobileViewOther(BuildContext context, double containerWidth) {
    return SizedBox(
      width: containerWidth,
      child: Column(
        children: [
          // Tabbed interface for PunchCards, Employees, and Locations
          Container(
            //height: 400, // Set appropriate height for tab content
            child: MyTabBar<int>(
              itemBuilder: (context, value) {
                switch (value) {
                  case 0:
                    return const _PunchCards(isMobileVP: true);
                  case 1:
                    return _Employees();
                  case 2:
                    return _Locations();
                  default:
                    return Container();
                }
              },
              children: [
                TabButton<int>(
                  value: 0,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                     // const Icon(Icons.credit_card_outlined, size: 15),
                  //    const SizedBox(width: 8),
                      Text(AppLocalization.of(context).punchCards),
                    ],
                  ),
                ),
                TabButton<int>(
                  value: 1,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                     // const Icon(Icons.people_outline, size: 15),
                //      const SizedBox(width: 8),
                      Text(AppLocalization.of(context).employees),
                    ],
                  ),
                ),
                TabButton<int>(
                  value: 2,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                     // const Icon(Icons.location_on_outlined, size: 15),
                  //    const SizedBox(width: 8),
                      Text(AppLocalization.of(context).locations),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          // Calendar remains below the tabs
          const TableEventsExample(),
        ],
      ),
    );
  }

  Widget _buildDesktopViewOther(double containerWidth) {
    return SizedBox(
      width: containerWidth,
      child: Row(
        children: [
          Expanded(child: _Employees()),
          const SizedBox(width: 20),
          Expanded(child: _Locations()),
          const SizedBox(width: 20),
          const Expanded(child: TableEventsExample()),
        ],
      ),
    );
  }

  Widget _buildCalendarForTablet() {
    return const SizedBox(
      width: 550,
      height: 550,
      child: TableEventsExample(),
    );
  }
}

// -----------------------------------------------------------------------------
// 3) Supporting widgets (PunchCardBox, employees/locations, etc.)
// -----------------------------------------------------------------------------

class PunchCardBox extends StatelessWidget {
  final String title;
  final int activeCount;
  final int inactiveCount;
  final VoidCallback onTap;

  const PunchCardBox({
    super.key,
    required this.title,
    required this.activeCount,
    required this.inactiveCount,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: DecoratedBox(
        decoration: BoxDecoration(
          border: Border.all(color: ColorHelper.thePunchDesktopLightGray()),
       //   borderRadius: BorderRadius.circular(32),
        ),
        child: Padding(
          padding: const EdgeInsets.all(10),
          child: Column(
            children: [
              Text(
                title.toUpperCase(),
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 10),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  PunchCardCountBoxGreen(
                    AppLocalization.of(context).active,
                    activeCount.toString(),
                  ),
                  const SizedBox(width: 10),
                  PunchCardCountBoxPink(
                    AppLocalization.of(context).inactive,
                    inactiveCount.toString(),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _Employees extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final employees =
        context.select<_ViewModel, Iterable<User>>((vm) => vm.employees);
    final activeCount = employees.where((e) => e.isActive).length;
    final inactiveCount = employees.where((e) => !e.isActive).length;

    return PunchCardBox(
      title: AppLocalization.of(context).employees,
      activeCount: activeCount,
      inactiveCount: inactiveCount,
      onTap: () => context.go('/employees'),
    );
  }
}

class _Locations extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final locations =
        context.select<_ViewModel, Iterable<Location>>((vm) => vm.locations);
    final activeCount = locations.where((e) => e.isActive).length;
    final inactiveCount = locations.where((e) => !e.isActive).length;

    return PunchCardBox(
      title: AppLocalization.of(context).locations,
      activeCount: activeCount,
      inactiveCount: inactiveCount,
      onTap: () => context.go('/locations'),
    );
  }
}

class _PunchCards extends StatelessWidget {
  final bool isMobileVP;
  const _PunchCards({required this.isMobileVP});

  @override
  Widget build(BuildContext context) {
    final punchCards =
        context.select<_ViewModel, Iterable<PunchCard>>((vm) => vm.punchCards);
    return _buildPunchCardTable(context, punchCards);
  }

  Widget _buildPunchCardTable(BuildContext context, Iterable<PunchCard> pcs) {
    final today = DateTimeExtension.todayUTC;
    final yesterday = DateTimeExtension.yesterdayUTC;
    final thisWeek = DateTimeExtension.thisWeekUTC;
    final lastWeek = DateTimeExtension.lastWeekUTC;
    final thisMonth = DateTimeExtension.thisMonthUTC;
    final lastMonth = DateTimeExtension.lastMonthUTC;

    final todayCount =
        pcs.where((e) => e.clockedIn > today.item1 && e.clockedIn <= today.item2).length;
    final yesterdayCount =
        pcs.where((e) => e.clockedIn > yesterday.item1 && e.clockedIn <= yesterday.item2)
            .length;
    final thisWeekCount =
        pcs.where((e) => e.clockedIn > thisWeek.item1 && e.clockedIn <= thisWeek.item2)
            .length;
    final lastWeekCount =
        pcs.where((e) => e.clockedIn > lastWeek.item1 && e.clockedIn <= lastWeek.item2)
            .length;
    final thisMonthCount =
        pcs.where((e) => e.clockedIn > thisMonth.item1 && e.clockedIn <= thisMonth.item2)
            .length;
    final lastMonthCount =
        pcs.where((e) => e.clockedIn > lastMonth.item1 && e.clockedIn <= lastMonth.item2)
            .length;

    return isMobileVP ? DecoratedBox(
      decoration: BoxDecoration(
        border: Border.all(color: ColorHelper.thePunchDesktopLightGray()),
      //  borderRadius: BorderRadius.circular(32),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              '${AppLocalization.of(context).punchCards.toUpperCase()} TODAY',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Text(
              '${todayCount}',
              style: Theme.of(context).textTheme.titleMedium,
            ),            
            const SizedBox(height: 10),
            LayoutBuilder(
              builder: (context, constraints) {
                double fontSize = constraints.maxWidth <= 400 ? 12 : 14;
                double numberFontSize = constraints.maxWidth <= 400 ? 32 : 64;
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                      Expanded(
                        flex: 1,
                        child: Text('This Week: ${thisWeekCount}'),
                      ),
                      SizedBox(width: 10),
                      Expanded(
                        flex: 1,
                        child: Text('This month: ${thisMonthCount}'),
                      ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                      Expanded(
                        flex: 1,
                        child: Text('Yesterday: ${yesterdayCount}'),
                      ),
                      SizedBox(width: 10),
                      Expanded(
                        flex: 1,
                        child: Text('Last Week: ${lastWeekCount}'),
                      ),
                      ],
                    ),
                    Row(
                      children: [
                      Expanded(
                        flex: 1,
                        child: Text('Last Month: ${lastMonthCount}'),
                      ),
                      ],
                    ),
                    ],
                  
                );
              },
            ),
          ],
        ),
      ),
    ): DecoratedBox(
      decoration: BoxDecoration(
        border: Border.all(color: ColorHelper.thePunchDesktopLightGray()),
        borderRadius: BorderRadius.circular(32),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalization.of(context).punchCards.toUpperCase(),
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 10),
            LayoutBuilder(
              builder: (context, constraints) {
                double fontSize = constraints.maxWidth <= 400 ? 12 : 14;
                double numberFontSize = constraints.maxWidth <= 400 ? 32 : 64;
                return _buildPunchCardCountTable(
                  context,
                  todayCount,
                  yesterdayCount,
                  thisWeekCount,
                  lastWeekCount,
                  thisMonthCount,
                  lastMonthCount,
                  fontSize,
                  numberFontSize,
                );
              },
            ),
          ],
        ),
      ),
    );
;
  }

  Widget _buildPunchCardCountTable(
    BuildContext context,
    int todayCount,
    int yesterdayCount,
    int thisWeekCount,
    int lastWeekCount,
    int thisMonthCount,
    int lastMonthCount,
    double fontSize,
    double numberFontSize,
  ) {
    return Table(
      columnWidths: const {
        0: FlexColumnWidth(),
        1: FlexColumnWidth(),
        2: FlexColumnWidth(),
      },
      children: [
        TableRow(
          children: [
            PunchCardCountBoxRed(
              AppLocalization.of(context).today,
              todayCount.toString(),
              fontSize: fontSize,
              numberFontSize: numberFontSize,
            ),
            PunchCardCountBox(
              AppLocalization.of(context).thisWeek,
              thisWeekCount.toString(),
              fontSize: fontSize,
              numberFontSize: numberFontSize,
            ),
            PunchCardCountBox(
              AppLocalization.of(context).thisMonth,
              thisMonthCount.toString(),
              fontSize: fontSize,
              numberFontSize: numberFontSize,
            ),
          ],
        ),
        TableRow(
          children: [
            PunchCardCountBox(
              AppLocalization.of(context).yesterday,
              yesterdayCount.toString(),
              fontSize: fontSize,
              numberFontSize: numberFontSize,
            ),
            PunchCardCountBox(
              AppLocalization.of(context).lastWeek,
              lastWeekCount.toString(),
              fontSize: fontSize,
              numberFontSize: numberFontSize,
            ),
            PunchCardCountBox(
              AppLocalization.of(context).lastMonth,
              lastMonthCount.toString(),
              fontSize: fontSize,
              numberFontSize: numberFontSize,
            ),
          ],
        ),
      ],
    );
  }
}

// PunchCardCountBox Widgets
class PunchCardCountBox extends StatelessWidget {
  final String title;
  final String count;
  final double fontSize;
  final double numberFontSize;

  const PunchCardCountBox(
    this.title,
    this.count, {
    super.key,
    required this.fontSize,
    required this.numberFontSize,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 187,
      height: 148,
      margin: const EdgeInsets.only(right: 10, bottom: 10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(32),
        border: Border.all(color: ColorHelper.thePunchDesktopLightGray()),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            title,
            style: TextStyle(fontSize: fontSize, fontWeight: FontWeight.w700),
          ),
          Text(
            count,
            style: TextStyle(
              fontSize: numberFontSize,
              fontWeight: FontWeight.w700,
            ),
          ),
        ],
      ),
    );
  }
}

class PunchCardCountBoxRed extends PunchCardCountBox {
  const PunchCardCountBoxRed(
    super.title,
    super.count, {
    super.key,
    required super.fontSize,
    required super.numberFontSize,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 187,
      height: 148,
      margin: const EdgeInsets.only(right: 10, bottom: 10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(32),
        color: ColorHelper.thePunchRed(),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.w700,
              color: Colors.white,
            ),
          ),
          Text(
            count,
            style: TextStyle(
              fontSize: numberFontSize,
              fontWeight: FontWeight.w700,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}

class PunchCardCountBoxGreen extends StatelessWidget {
  const PunchCardCountBoxGreen(this.title, this.count, {super.key});
  final String title;
  final String count;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 120,
      //height: 150,
      margin: const EdgeInsets.only(right: 10, bottom: 10),
      decoration: BoxDecoration(
     //   borderRadius: BorderRadius.circular(32),
        border: Border.all(color: ColorHelper.thePunchBorderGreen()),
        color: ColorHelper.thePunchGreen(),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w700,
              color: ColorHelper.thePunchLightGreen(),
            ),
          ),
          Text(
            count,
            style: TextStyle(
              fontSize: 34,
              fontWeight: FontWeight.w700,
              color: ColorHelper.thePunchLightGreen(),
            ),
          ),
        ],
      ),
    );
  }
}

class PunchCardCountBoxPink extends StatelessWidget {
  const PunchCardCountBoxPink(this.title, this.count, {super.key});
  final String title;
  final String count;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 120,
     // height: 150,
      margin: const EdgeInsets.only(right: 10, bottom: 10),
      decoration: BoxDecoration(
      //  borderRadius: BorderRadius.circular(32),
        border: Border.all(width: 1, color: ColorHelper.thePunchBorderRed()),
        color: ColorHelper.thePunchLightRed(),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w700,
              color: ColorHelper.thePunchAccentRed(),
            ),
          ),
          Text(
            count,
            style: TextStyle(
              fontSize: 34,
              fontWeight: FontWeight.w700,
              color: ColorHelper.thePunchAccentRed(),
            ),
          ),
        ],
      ),
    );
  }
}

// PunchCardBigCountBoxRed
class PunchCardBigCountBoxRed extends StatelessWidget {
  const PunchCardBigCountBoxRed(this.title, this.count, {super.key});
  final String title;
  final String count;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 300,
      height: 400,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(32),
        color: ColorHelper.thePunchRed(),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Padding(padding: EdgeInsets.only(top: 15)),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w700,
              color: Colors.white,
            ),
          ),
          Text(
            count,
            style: const TextStyle(
              fontSize: 100,
              fontWeight: FontWeight.w700,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}

// -----------------------------------------------------------------------------
// 4) The ViewModel used by HomePage
// -----------------------------------------------------------------------------

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  Location? organization;
  User? currentUser;
  Iterable<Location> locations = [];
  Iterable<User> employees = [];
  Iterable<PunchCard> punchCards = [];
  Iterable<Schedule> schedules = [];
  Iterable<Inspection> inspections = [];
  bool isLoading = true;
  late PermissionsState permissionsState;

  int? get allCount => punchCards.length;

  // Make punchCard late
  PunchCard? globalPunchCard;

  _ViewModel() {
    addListenables([
      DataModel().locationModel,
      DataModel().userModel,
      DataModel().scheduleModel,
      DataModel().punchCardModel,
      DataModel().inspectionModel,
      LoginState.userNotifier,
    ]);
  }

  Future<void> initialize(BuildContext context) async {
    permissionsState = Provider.of<PermissionsState>(context, listen: false);
    await refresh();
    isLoading = false;
    notifyListeners();
  }

  @override
  Future<void> refresh() async {
    try {
      isLoading = true;
      notifyListeners();

      locations = await DataModel().locationModel.all;
      employees = await DataModel().userModel.allEmployees;
      punchCards = await DataModel().punchCardModel.active;
      schedules = await DataModel().scheduleModel.active;
      inspections = await DataModel().inspectionModel.active;
      organization = locations.firstWhereOrNull((e) => e.id == Location.primaryLocationId);
      currentUser = LoginState.userNotifier.value;
    } catch (e) {
      print('Error loading data: $e');
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  // -----------------------------------------
  // Example: Create & Save PunchCard
  // -----------------------------------------
  Future<void> createPunchCard() async {
    try {
      // Replace with actual IDs from your app’s logic:
      final String locationId = 'AA48352C-A563-4717-B230-4CB8DDB7D5F6';
      final String scheduleId = 'schedule_id_here';
      final String jobTypeId = 'F13A4567-1234-5678-ABCD-9876543210FE';
      final String linkId = '2c510104-33d5-4cbd-9280-242d32e773e8';

      final punchCard = PunchCard.create()
        ..userId = LoginState.userId
        ..clockedIn = ServerTimeState().utcTime
        ..locationId = locationId
        ..jobTypeId = jobTypeId
        ..punchCardLinkId = linkId;

      globalPunchCard = punchCard;

      // Save it
      await DataModel().punchCardModel.savePunchCard(punchCard);
      print('MyAppDebug: Regular punch card saved: $punchCard');
    } catch (error) {
      print('Error saving punch card: $error');
    }
  }

  // -----------------------------------------
  // Example: End a PunchCard (Clock Out)
  // -----------------------------------------
  Future<void> endPunchCard() async {
    try {
      // Set the clock-out time
      globalPunchCard!.clockedOut = ServerTimeState().utcTime;

      // Save updated
      await DataModel().punchCardModel.savePunchCard(globalPunchCard!);

      print('MyAppDebug: Punch card ended (clocked out): $globalPunchCard');

      // Then refresh data
      await refresh();
    } catch (error) {
      print('Error ending punch card: $error');
    }
  }
}

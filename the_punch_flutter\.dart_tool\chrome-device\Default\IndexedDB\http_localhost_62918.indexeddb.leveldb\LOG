2025/06/25-12:02:29.804 5b7c Reusing MANIFEST C:\Users\<USER>\AppData\Local\Temp\flutter_tools.402a786c\flutter_tools_chrome_device.7935aea\Default\IndexedDB\http_localhost_62918.indexeddb.leveldb/MANIFEST-000001
2025/06/25-12:02:29.804 5b7c Recovering log #37
2025/06/25-12:02:29.832 5b7c Reusing old log C:\Users\<USER>\AppData\Local\Temp\flutter_tools.402a786c\flutter_tools_chrome_device.7935aea\Default\IndexedDB\http_localhost_62918.indexeddb.leveldb/000037.log 
2025/06/25-12:02:29.833 5b7c Delete type=2 #35
2025/06/25-12:02:29.833 5b7c Delete type=2 #36
2025/06/25-12:02:29.833 5b7c Delete type=2 #38
2025/06/25-12:02:29.855 6380 Level-0 table #43: started
2025/06/25-12:02:29.876 6380 Level-0 table #43: 760034 bytes OK
2025/06/25-12:02:29.878 6380 Delete type=0 #37
2025/06/25-12:02:29.879 6380 Manual compaction at level-0 from '\x00 \x00\x00\x00' @ 72057594037927935 : 1 .. '\x00!\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/25-12:02:29.928 224c Compacting 1@1 + 2@2 files
2025/06/25-12:02:29.986 224c Generated table #44@1: 11254 keys, 2165103 bytes
2025/06/25-12:02:30.036 224c Generated table #45@1: 15543 keys, 2169830 bytes
2025/06/25-12:02:30.045 224c Generated table #46@1: 3401 keys, 192151 bytes
2025/06/25-12:02:30.046 224c Compacted 1@1 + 2@2 files => 4527084 bytes
2025/06/25-12:02:30.047 224c compacted to: files[ 0 0 3 0 0 0 0 ]
